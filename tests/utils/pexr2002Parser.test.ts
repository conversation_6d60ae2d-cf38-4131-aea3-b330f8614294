/**
 * PEXR2002 Parser Tests
 * 
 * Tests for the PEXR2002 SAP payment file parser to ensure proper
 * extraction of reference, amount, payee, and other fields.
 */

import { describe, it, expect } from 'vitest';
import { processPaymentFile } from '../../server/utils/fileProcessors';

describe('PEXR2002 Parser', () => {
  it('should correctly parse SAP EDI PEXR2002 format', () => {
    const samplePEXR2002 = `EDI_DC40 TABNAM EDIDC40 MANDT 100 DOCNUM ********** STATUS 30 DIRECT 2 DOCREL 740 TEST N IDOCTYP PEXR2002 CIMTYP PEXR2002 MESTYP PAYEXT SNDPRT LS SNDPFC PY SNDPRN AsiaConnectPteLtd RCVPRT LS RCVPFC PY RCVPRN EuroFinanceSA CREDAT ******** CRETIM 101525 SERIAL 202300001

/* Payment File #1 - PEXR2002 Format */
/* Common Reference: REF-************ */
E1PAYHDR DOCNUM ********** CATEGORY PAYMENT PAYID REF-************ PAYTYPE DOMESTIC
E1PAYREF DOCNUM ********** REFDOCNUM REF-************
E1PAYORG DOCNUM ********** SENDERNAME AsiaConnect Pte Ltd SENDERIBAN ********************** SENDERCTY SG SENDERADRCTY Singapore
E1PAYDEST DOCNUM ********** RECEIVERNM EuroFinance SA RECEIVERIBAN *************************** RECEIVERCTY FR RECEIVERADRCTY Paris
E1PAYAMT DOCNUM ********** CURRENCY USD DOCAMT 349999
E1PAYINF DOCNUM ********** PURPOSE Payment #1 for Software Licenses (SWL-2023) - Ref: REF-************
E1PAYDAT DOCNUM ********** DOCDATE ******** VALUEDATE ********`;

    const result = processPaymentFile(samplePEXR2002, 'PEXR2002');

    // Check that the reference is correctly extracted from comment
    expect(result.reference).toBe('REF-************');
    
    // Check that the amount is correctly parsed (349999 cents = $3499.99)
    expect(result.amount).toBe(3499.99);
    
    // Check that the sender is correctly extracted
    expect(result.sender).toBe('AsiaConnect Pte Ltd');
    
    // Check that the recipient is correctly extracted
    expect(result.recipient).toBe('EuroFinance SA');
    
    // Check that the recipient IBAN is correctly extracted
    expect(result.recipient_account).toBe('***************************');
    
    // Check that the due date is correctly parsed (April 12, 2025)
    expect(result.due_date).toEqual(new Date(2025, 3, 12)); // Month is 0-based
    
    // Check that the file type is set correctly
    expect(result.file_type).toBe('PEXR2002');
  });

  it('should handle simple line-based PEXR2002 format', () => {
    const simplePEXR2002 = `PEXR2002
HDR:********
REF:REF-********-001
AMT:1500.50
SND:Test Company Inc
RCV:Vendor Ltd
RECEIVERIBAN:**********************
DUE:2025-01-15`;

    const result = processPaymentFile(simplePEXR2002, 'PEXR2002');

    // Check that the amount is correctly parsed
    expect(result.amount).toBe(1500.50);
    
    // Check that the reference is correctly extracted
    expect(result.reference).toBe('REF-********-001');
    
    // Check that the sender and recipient are correctly extracted
    expect(result.sender).toBe('Test Company Inc');
    expect(result.recipient).toBe('Vendor Ltd');
    
    // Check that the recipient address (IBAN) is correctly extracted
    expect(result.recipient_address).toBe('**********************');
  });

  it('should auto-detect PEXR2002 format', () => {
    const samplePEXR2002 = `/* Common Reference: REF-************ */
E1PAYHDR DOCNUM ********** PAYID REF-************
E1PAYAMT DOCNUM ********** DOCAMT 100000
E1PAYDEST DOCNUM ********** RECEIVERNM Test Vendor`;

    // Don't specify format - let it auto-detect
    const result = processPaymentFile(samplePEXR2002);

    expect(result.file_type).toBe('PEXR2002');
    expect(result.reference).toBe('REF-************');
    expect(result.amount).toBe(1000.00); // 100000 cents = $1000.00
  });

  it('should handle malformed PEXR2002 gracefully', () => {
    const malformedPEXR2002 = `PEXR2002
MALFORMED DATA`;

    const result = processPaymentFile(malformedPEXR2002, 'PEXR2002');

    // Should not throw an error and should have fallback values
    expect(result.file_type).toBe('PEXR2002');
    expect(result.amount).toBe(1000.00); // Fallback amount
    expect(result.sender).toBe('Company Inc.'); // Fallback sender
    expect(result.recipient).toBe('Vendor LLC'); // Fallback recipient
  });
});
