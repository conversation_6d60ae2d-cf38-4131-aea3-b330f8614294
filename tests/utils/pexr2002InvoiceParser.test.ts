/**
 * PEXR2002 Invoice Parser Tests
 * 
 * Tests for the PEXR2002 SAP invoice file parser to ensure proper
 * extraction of reference, amount, customer, and other fields for AR.
 */

import { describe, it, expect } from 'vitest';
import { processInvoiceFile } from '../../server/utils/fileProcessors';

describe('PEXR2002 Invoice Parser', () => {
  it('should correctly parse SAP EDI PEXR2002 invoice format', () => {
    const samplePEXR2002Invoice = `EDI_DC40 TABNAM EDIDC40 MANDT 100 DOCNUM 0000000001 STATUS 30 DIRECT 2 DOCREL 740 TEST N IDOCTYP PEXR2002 CIMTYP PEXR2002 MESTYP INVEXT SNDPRT LS SNDPFC IV SNDPRN GlobalLogistics RCVPRT LS RCVPFC IV RCVPRN TechCorpInc CREDAT 20250412 CRETIM 101525 SERIAL 202300001

/* Invoice File #1 - PEXR2002 Format */
/* Common Reference: INV-************ */
E1INVHDR DOCNUM 0000000001 CATEGORY INVOICE INVID INV-************ INVTYPE STANDARD
E1INVREF DOCNUM 0000000001 REFDOCNUM INV-************
E1INVORG DOCNUM 0000000001 SUPPLIERNM Global Logistics Ltd SUPPLIERIBAN ********************** SUPPLIERCTY US SUPPLIERADRCTY New York
E1INVDEST DOCNUM 0000000001 CUSTOMERNM TechCorp Inc CUSTOMERIBAN ********************** CUSTOMERCTY US CUSTOMERADRCTY San Francisco
E1INVAMT DOCNUM 0000000001 CURRENCY USD DOCAMT 2500000
E1INVINF DOCNUM 0000000001 PURPOSE Invoice #1 for Software Development Services - Ref: INV-************
E1INVDAT DOCNUM 0000000001 INVDATE 20250412 DUEDATE 20250512`;

    const result = processInvoiceFile(samplePEXR2002Invoice, 'PEXR2002');

    // Check that the reference is correctly extracted from comment
    expect(result.reference).toBe('INV-************');
    
    // Check that the amount is correctly parsed (2500000 cents = $25000.00)
    expect(result.amount).toBe(25000.00);
    
    // Check that the customer is correctly extracted
    expect(result.customer).toBe('TechCorp Inc');
    
    // Check that the description is correctly extracted
    expect(result.description).toContain('Software Development Services');
    
    // Check that the due date is correctly parsed (May 12, 2025)
    expect(result.due_date).toEqual(new Date(2025, 4, 12)); // Month is 0-based
    
    // Check that the file type is set correctly
    expect(result.file_type).toBe('PEXR2002');
  });

  it('should handle PEXR2002 with payment segments (E1PAY) for invoice context', () => {
    const samplePEXR2002 = `EDI_DC40 TABNAM EDIDC40 MANDT 100 DOCNUM 0000000001 STATUS 30 DIRECT 2 DOCREL 740 TEST N IDOCTYP PEXR2002 CIMTYP PEXR2002 MESTYP PAYEXT SNDPRT LS SNDPFC PY SNDPRN GlobalLogistics RCVPRT LS RCVPFC PY RCVPRN TechCorpInc CREDAT 20250412 CRETIM 101525 SERIAL 202300001

/* Invoice File #1 - PEXR2002 Format */
/* Common Reference: INV-************ */
E1PAYHDR DOCNUM 0000000001 CATEGORY PAYMENT PAYID INV-************ PAYTYPE DOMESTIC
E1PAYREF DOCNUM 0000000001 REFDOCNUM INV-************
E1PAYORG DOCNUM 0000000001 SENDERNAME Global Logistics Ltd SENDERIBAN ********************** SENDERCTY US SENDERADRCTY New York
E1PAYDEST DOCNUM 0000000001 RECEIVERNM TechCorp Inc RECEIVERIBAN ********************** RECEIVERCTY US RECEIVERADRCTY San Francisco
E1PAYAMT DOCNUM 0000000001 CURRENCY USD DOCAMT 1500000
E1PAYINF DOCNUM 0000000001 PURPOSE Invoice payment for consulting services - Ref: INV-************
E1PAYDAT DOCNUM 0000000001 DOCDATE 20250412 VALUEDATE 20250512`;

    const result = processInvoiceFile(samplePEXR2002, 'PEXR2002');

    // Check that the reference is correctly extracted from comment
    expect(result.reference).toBe('INV-************');
    
    // Check that the amount is correctly parsed (1500000 cents = $15000.00)
    expect(result.amount).toBe(15000.00);
    
    // Check that the customer is correctly extracted from RECEIVERNM
    expect(result.customer).toBe('TechCorp Inc');
    
    // Check that the description is correctly extracted
    expect(result.description).toContain('consulting services');
    
    // Check that the due date is correctly parsed from VALUEDATE
    expect(result.due_date).toEqual(new Date(2025, 4, 12)); // Month is 0-based
    
    // Check that the file type is set correctly
    expect(result.file_type).toBe('PEXR2002');
  });

  it('should handle simple line-based PEXR2002 invoice format', () => {
    const simplePEXR2002Invoice = `PEXR2002
HDR:20250101
INVREF:INV-20250101-001
INVAMT:3500.75
CUSTOMER:Customer Corp Ltd
PURPOSE:Monthly subscription services
DUEDATE:2025-01-31`;

    const result = processInvoiceFile(simplePEXR2002Invoice, 'PEXR2002');

    // Check that the amount is correctly parsed
    expect(result.amount).toBe(3500.75);
    
    // Check that the reference is correctly extracted
    expect(result.reference).toBe('INV-20250101-001');
    
    // Check that the customer is correctly extracted
    expect(result.customer).toBe('Customer Corp Ltd');
    
    // Check that the description is correctly extracted
    expect(result.description).toBe('Monthly subscription services');
    
    // Check that the due date is correctly parsed (use date string comparison to avoid timezone issues)
    expect(result.due_date.toISOString().split('T')[0]).toBe('2025-01-31');
  });

  it('should auto-detect PEXR2002 invoice format', () => {
    const samplePEXR2002Invoice = `/* Common Reference: INV-20250412-003 */
E1INVHDR DOCNUM 0000000001 INVID INV-20250412-003
E1INVAMT DOCNUM 0000000001 DOCAMT 500000
E1INVDEST DOCNUM 0000000001 CUSTOMERNM Test Customer Inc`;

    // Don't specify format - let it auto-detect
    const result = processInvoiceFile(samplePEXR2002Invoice);

    expect(result.file_type).toBe('PEXR2002');
    expect(result.reference).toBe('INV-20250412-003');
    expect(result.amount).toBe(5000.00); // 500000 cents = $5000.00
    expect(result.customer).toBe('Test Customer Inc');
  });

  it('should handle malformed PEXR2002 invoice gracefully', () => {
    const malformedPEXR2002Invoice = `PEXR2002
MALFORMED INVOICE DATA`;

    const result = processInvoiceFile(malformedPEXR2002Invoice, 'PEXR2002');

    // Should not throw an error and should have fallback values
    expect(result.file_type).toBe('PEXR2002');
    expect(result.amount).toBe(1000.00); // Fallback amount
    expect(result.customer).toBe('Customer Inc.'); // Fallback customer
    expect(result.description).toContain('Invoice imported from PEXR2002 file');
    expect(result.reference).toMatch(/^INV-\d+$/); // Generated reference
  });
});
