/**
 * Liquidity Forecast Tests
 * 
 * Tests for the liquidity runway projection to ensure proper
 * cash flow forecasting based on upcoming payments and invoices.
 */

import { describe, it, expect } from 'vitest';
import { getLiquidityRunwayProjection } from '../../client/src/lib/demoData';

describe('Liquidity Forecast', () => {
  it('should calculate liquidity runway with upcoming payments and invoices', () => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    // Mock payments that haven't been sent yet (should be outflows)
    const upcomingPayments = [
      {
        id: 1,
        reference: 'PAY-001',
        amount: 10000,
        status: 'Not Approved',
        approved: false,
        sent_at: null,
        due_date: tomorrow.toISOString().split('T')[0],
        recipient: 'Vendor A'
      },
      {
        id: 2,
        reference: 'PAY-002',
        amount: 5000,
        status: 'Approved',
        approved: true,
        sent_at: null,
        due_date: nextWeek.toISOString().split('T')[0],
        recipient: 'Vendor B'
      }
    ];

    // Mock open invoices (should be inflows when paid)
    const openInvoices = [
      {
        id: 1,
        reference: 'INV-001',
        amount: 15000,
        status: 'Open',
        due_date: tomorrow.toISOString().split('T')[0],
        customer: 'Customer A'
      },
      {
        id: 2,
        reference: 'INV-002',
        amount: 8000,
        status: 'Overdue',
        due_date: nextWeek.toISOString().split('T')[0],
        customer: 'Customer B'
      }
    ];

    // Mock current stablecoin balances
    const stablecoinBalances = [
      { symbol: 'USDC', amount: 100000 },
      { symbol: 'USDT', amount: 50000 }
    ];

    const projection = getLiquidityRunwayProjection(upcomingPayments, openInvoices, stablecoinBalances);

    // Should have 30 data points (30 days)
    expect(projection).toHaveLength(30);

    // Starting balance should be total stablecoin balance
    expect(projection[0].value).toBe(150000);

    // Find the day with the payment and invoice due tomorrow
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    const tomorrowPoint = projection.find(p => p.date === tomorrowStr);
    
    if (tomorrowPoint) {
      // Tomorrow should have net inflow of +5000 (15000 inflow - 10000 outflow)
      // So balance should be 150000 + 5000 = 155000
      expect(tomorrowPoint.value).toBe(155000);
    }

    // Find the day with the payment and invoice due next week
    const nextWeekStr = nextWeek.toISOString().split('T')[0];
    const nextWeekPoint = projection.find(p => p.date === nextWeekStr);
    
    if (nextWeekPoint) {
      // Next week should have net inflow of +3000 (8000 inflow - 5000 outflow)
      // Starting from 155000 (after tomorrow's transactions) + 3000 = 158000
      expect(nextWeekPoint.value).toBe(158000);
    }
  });

  it('should handle payments that have already been sent', () => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Mock payments - one sent, one not sent
    const payments = [
      {
        id: 1,
        reference: 'PAY-001',
        amount: 10000,
        status: 'Sent',
        approved: true,
        sent_at: today.toISOString(),
        due_date: tomorrow.toISOString().split('T')[0],
        recipient: 'Vendor A'
      },
      {
        id: 2,
        reference: 'PAY-002',
        amount: 5000,
        status: 'Approved',
        approved: true,
        sent_at: null,
        due_date: tomorrow.toISOString().split('T')[0],
        recipient: 'Vendor B'
      }
    ];

    const stablecoinBalances = [{ symbol: 'USDC', amount: 100000 }];

    const projection = getLiquidityRunwayProjection(payments, [], stablecoinBalances);

    // Find tomorrow's projection
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    const tomorrowPoint = projection.find(p => p.date === tomorrowStr);
    
    if (tomorrowPoint) {
      // Only the unsent payment (5000) should affect the forecast
      // So balance should be 100000 - 5000 = 95000
      expect(tomorrowPoint.value).toBe(95000);
    }
  });

  it('should handle invoices that have been paid', () => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Mock invoices - one paid, one open
    const invoices = [
      {
        id: 1,
        reference: 'INV-001',
        amount: 15000,
        status: 'Paid',
        payment_id: 123,
        due_date: tomorrow.toISOString().split('T')[0],
        customer: 'Customer A'
      },
      {
        id: 2,
        reference: 'INV-002',
        amount: 8000,
        status: 'Open',
        due_date: tomorrow.toISOString().split('T')[0],
        customer: 'Customer B'
      }
    ];

    const stablecoinBalances = [{ symbol: 'USDC', amount: 100000 }];

    const projection = getLiquidityRunwayProjection([], invoices, stablecoinBalances);

    // Find tomorrow's projection
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    const tomorrowPoint = projection.find(p => p.date === tomorrowStr);
    
    if (tomorrowPoint) {
      // Only the unpaid invoice (8000) should affect the forecast
      // So balance should be 100000 + 8000 = 108000
      expect(tomorrowPoint.value).toBe(108000);
    }
  });

  it('should use demo data when no real data is available', () => {
    const stablecoinBalances = [{ symbol: 'USDC', amount: 100000 }];

    const projection = getLiquidityRunwayProjection([], [], stablecoinBalances);

    // Should still have 30 data points
    expect(projection).toHaveLength(30);

    // Starting balance should be close to the stablecoin balance (demo data adds some variation)
    expect(projection[0].value).toBeGreaterThan(80000);
    expect(projection[0].value).toBeLessThan(120000);

    // Should have some variation due to demo data
    const hasVariation = projection.some((point, index) => {
      if (index === 0) return false;
      return point.value !== projection[0].value;
    });
    expect(hasVariation).toBe(true);
  });
});
