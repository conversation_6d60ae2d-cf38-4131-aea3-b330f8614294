/**
 * MT103 Parser Tests
 * 
 * Tests for the MT103 SWIFT payment file parser to ensure proper
 * extraction of amount, date, and other fields from the :32A: field.
 */

import { describe, it, expect } from 'vitest';
import { processPaymentFile } from '../../server/utils/fileProcessors';

describe('MT103 Parser', () => {
  it('should correctly parse the :32A: field with European decimal format', () => {
    const sampleMT103 = `{1:F01YOURBANKAXX0000000000}
{2:I103RECVBANKAXXXN}
{4:
/* Payment File #1 - MT103 Format */
/* Common Reference: REF-********-006 */
:20:REF-********-006
:23B:CRED
:32A:250417USD32049,99
:50K:/DE12345678901234567890
MediPharma GmbH
Berlin, DE
:53A:YOURBANKAXX
:57A:RECVBANKAXX
:59:/NO9876543210987654321
NordicTrade AS
Oslo, NO
:70:Payment #1 for Software Licenses
REF: REF-********-006
CODE: SWL-2023
:71A:SHA
-}`;

    const result = processPaymentFile(sampleMT103, 'MT103');

    // Check that the amount is correctly parsed
    expect(result.amount).toBe(32049.99);
    
    // Check that the reference is correctly extracted
    expect(result.reference).toBe('REF-********-006');
    
    // Check that the sender is correctly extracted
    expect(result.sender).toContain('MediPharma GmbH');
    
    // Check that the recipient is correctly extracted
    expect(result.recipient).toContain('NordicTrade AS');
    
    // Check that the due date is correctly parsed (April 17, 2025)
    expect(result.due_date).toEqual(new Date(2025, 3, 17)); // Month is 0-based
    
    // Check that the file type is set correctly
    expect(result.file_type).toBe('MT103');
  });

  it('should handle MT103 with US decimal format', () => {
    const sampleMT103 = `{1:F01YOURBANKAXX0000000000}
{2:I103RECVBANKAXXXN}
{4:
:20:REF-********-001
:23B:CRED
:32A:250101USD1500.50
:50K:/US12345678901234567890
Test Company Inc
New York, NY
:59:/GB9876543210987654321
Vendor Ltd
London, UK
:71A:SHA
-}`;

    const result = processPaymentFile(sampleMT103, 'MT103');

    // Check that the amount is correctly parsed
    expect(result.amount).toBe(1500.50);
    
    // Check that the due date is correctly parsed (January 1, 2025)
    expect(result.due_date).toEqual(new Date(2025, 0, 1)); // Month is 0-based
  });

  it('should handle malformed :32A: field gracefully', () => {
    const sampleMT103 = `{1:F01YOURBANKAXX0000000000}
{2:I103RECVBANKAXXXN}
{4:
:20:REF-********-002
:23B:CRED
:32A:MALFORMED
:50K:/US12345678901234567890
Test Company Inc
:59:/GB9876543210987654321
Vendor Ltd
:71A:SHA
-}`;

    const result = processPaymentFile(sampleMT103, 'MT103');

    // Should not throw an error and should have default values
    expect(result.amount).toBe(0);
    expect(result.reference).toBe('REF-********-002');
    expect(result.due_date).toBeInstanceOf(Date);
  });

  it('should auto-detect MT103 format', () => {
    const sampleMT103 = `{1:F01YOURBANKAXX0000000000}
{2:I103RECVBANKAXXXN}
{4:
:20:REF-********-006
:32A:250417USD32049,99
:50K:/DE12345678901234567890
Test Company Inc
Berlin, DE
:59:/NO9876543210987654321
Vendor Company Ltd
Oslo, NO
-}`;

    // Don't specify format - let it auto-detect
    const result = processPaymentFile(sampleMT103);

    expect(result.file_type).toBe('MT103');
    expect(result.amount).toBe(32049.99);
  });
});
